{"name": "flystay", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fontsource/poppins": "^5.2.6", "@reduxjs/toolkit": "^2.8.2", "@stripe/react-stripe-js": "^3.9.1", "@stripe/stripe-js": "^7.8.0", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-table": "^8.21.3", "axios": "^1.9.0", "js-cookie": "^3.0.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-pro-sidebar": "^1.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.3", "react-spinners": "^0.17.0", "recharts": "^3.1.2", "sonner": "^2.0.3", "tailwindcss": "^4.1.4", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/xlsx": "^0.0.36", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}